import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In, IsNull, Not } from 'typeorm';
import { TestCase } from './test-case.entity';
import { CreateTestCaseDto } from './dto/create-test-case.dto';
import { ProjectsService } from '../projects/projects.service';
import { TestCaseFolder } from './folder.entity';
import { CreateFolderDto } from './dto/create-folder.dto';
import { MoveItemDto } from './dto/move-item.dto';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Platform, Priority, TestCaseType } from './test-case.entity';
import { parse } from 'csv-parse/sync';
import { Tag } from './tag.entity';
import { ImportJob, ImportJobStatus } from './import-job.entity';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import { User } from '../users/user.entity';
import { AutomationTestCase } from './automation-test-case.entity';
import { AutomationTestCaseDto } from './dto/automation-test-case.dto';
import { TempTestResult } from '../temp-test-results/temp-test-result.entity';
import axios from 'axios';


@Injectable()
export class TestCasesService {
  constructor(
    @InjectRepository(TestCase)
    private testCasesRepository: Repository<TestCase>,
    @InjectRepository(AutomationTestCase)
    private automationTestCasesRepository: Repository<AutomationTestCase>,
    @InjectRepository(TestCaseFolder)
    private foldersRepository: Repository<TestCaseFolder>,
    @InjectRepository(Tag)
    private tagsRepository: Repository<Tag>,
    @InjectRepository(ImportJob)
    private importJobRepository: Repository<ImportJob>,
    @InjectRepository(User)
    private usersRepository: Repository<User>,
    @InjectRepository(TempTestResult)
    private tempTestResultRepository: Repository<TempTestResult>,
    private projectsService: ProjectsService,
    private eventEmitter: EventEmitter2,
  ) {}

  async create(projectId: string, userId: string, createTestCaseDto: CreateTestCaseDto): Promise<TestCase> {
    // First, get the user to find their companyId and name/email
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Get the next tcId for this project
    const maxTcId = await this.testCasesRepository
      .createQueryBuilder('test_case')
      .where('test_case.projectId = :projectId', { projectId })
      .select('MAX(test_case.tcId)', 'max')
      .getRawOne();

    const nextTcId = (maxTcId?.max || 0) + 1;

    // Create base test case without tags
    const testCase = new TestCase();
    Object.assign(testCase, {
      ...createTestCaseDto,
      projectId,
      tcId: nextTcId,
      tags: [],
      createdBy: user.name || user.email,
    });

    // Handle tags if provided
    if (createTestCaseDto.tags?.length) {
      const tags = await this.tagsRepository.findBy({ id: In(createTestCaseDto.tags) });
      if (tags.length !== createTestCaseDto.tags.length) {
        throw new BadRequestException('One or more tags not found');
      }
      testCase.tags = tags;
    }

    const savedTestCase = await this.testCasesRepository.save(testCase);

    // Emit the test case created event
    this.eventEmitter.emit('testCase.created', { projectId: projectId, testCaseId: savedTestCase.id });

    return savedTestCase;
  }

  async findAllWithoutPagination(
    projectId: string,
    userId: string,
    options: {
      search?: string;
      folderId?: string;
      sortField?: string;
      sortDirection?: string;
      filters?: {
        priority: string[];
        platform: string[];
        testCaseType: string[];
        type: string[];
        tagIds: string[];
      }
    }
  ): Promise<TestCase[]> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    const { search, folderId, sortField, sortDirection, filters } = options;

    // Build query
    const queryBuilder = this.testCasesRepository.createQueryBuilder('testCase')
      .where('testCase.projectId = :projectId', { projectId })
      .leftJoinAndSelect('testCase.tags', 'tags');

    // Apply sorting
    if (sortField && ['tcId', 'title', 'type', 'priority', 'platform', 'testCaseType'].includes(sortField)) {
      queryBuilder.orderBy(`testCase.${sortField}`, sortDirection as 'ASC' | 'DESC');
    } else {
      // Default sorting
      queryBuilder.orderBy('testCase.tcId', 'ASC');
    }

    // Add search filter if provided
    if (search) {
      // Extract numeric part if search is in "TC-123" format
      const numericSearch = search.toLowerCase().startsWith('tc-') ? search.substring(3) : search;

      queryBuilder.andWhere(
        '(testCase.title LIKE :search OR ' +
        'CAST(testCase.tcId AS TEXT) LIKE :searchNumeric OR ' +
        'CAST(testCase.type AS TEXT) LIKE :search OR ' +
        'CAST(testCase.priority AS TEXT) LIKE :search OR ' +
        'CAST(testCase.platform AS TEXT) LIKE :search OR ' +
        'CAST(testCase.testCaseType AS TEXT) LIKE :search OR ' +
        'EXISTS (SELECT 1 FROM testcase_tags tt JOIN tags t ON tt."tagId" = t.id WHERE tt."testcaseId" = testCase.id AND t.name LIKE :search))',
        {
          search: `%${search}%`,
          searchNumeric: `%${numericSearch}%`
        }
      );
    }

    // Add folder filter if provided
    if (folderId) {
      queryBuilder.andWhere('testCase.folderId = :folderId', { folderId });
    } else if (folderId === '') {
      // Handle null folder case (root level)
      queryBuilder.andWhere('testCase.folderId IS NULL');
    }

    // Apply advanced filters
    if (filters) {
      // Priority filter
      if (filters.priority && filters.priority.length > 0) {
        queryBuilder.andWhere('testCase.priority IN (:...priorities)', {
          priorities: filters.priority.map(p => p.toLowerCase())
        });
      }

      // Platform filter
      if (filters.platform && filters.platform.length > 0) {
        queryBuilder.andWhere('testCase.platform IN (:...platforms)', {
          platforms: filters.platform.map(p => p.toLowerCase())
        });
      }

      // Test Case Type filter
      if (filters.testCaseType && filters.testCaseType.length > 0) {
        queryBuilder.andWhere('testCase.testCaseType IN (:...testCaseTypes)', {
          testCaseTypes: filters.testCaseType.map(t => t.toLowerCase())
        });
      }

      // Type filter
      if (filters.type && filters.type.length > 0) {
        queryBuilder.andWhere('LOWER(testCase.type) IN (:...types)', {
          types: filters.type.map(t => t.toLowerCase())
        });
      }

      // Tag filter
      if (filters.tagIds && filters.tagIds.length > 0) {
        queryBuilder.andWhere(
          'EXISTS (SELECT 1 FROM testcase_tags tt WHERE tt."testcaseId" = testCase.id AND tt."tagId" IN (:...tagIds))',
          { tagIds: filters.tagIds }
        );
      }
    }

    // Return all results without pagination
    return queryBuilder.getMany();
  }

  async findAll(
    projectId: string,
    userId: string,
    options: {
      page: number;
      limit: number;
      search?: string;
      folderId?: string;
      sortField?: string;
      sortDirection?: string;
      filters?: {
        priority: string[];
        platform: string[];
        testCaseType: string[];
        type: string[];
        tagIds: string[];
      }
    }
  ): Promise<{ data: TestCase[]; total: number }> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    const { page, limit, search, folderId, sortField, sortDirection, filters } = options;

    // Build query
    const queryBuilder = this.testCasesRepository.createQueryBuilder('testCase')
      .where('testCase.projectId = :projectId', { projectId })
      .leftJoinAndSelect('testCase.tags', 'tags');

    // Apply sorting
    if (sortField && ['tcId', 'title', 'type', 'priority', 'platform', 'testCaseType'].includes(sortField)) {
      queryBuilder.orderBy(`testCase.${sortField}`, sortDirection as 'ASC' | 'DESC');
    } else {
      // Default sorting
      queryBuilder.orderBy('testCase.tcId', 'ASC');
    }

    // Add search filter if provided
    if (search) {
      // Extract numeric part if search is in "TC-123" format
      const numericSearch = search.toLowerCase().startsWith('tc-') ? search.substring(3) : search;

      queryBuilder.andWhere(
        '(testCase.title LIKE :search OR ' +
        'CAST(testCase.tcId AS TEXT) LIKE :searchNumeric OR ' +
        'CAST(testCase.type AS TEXT) LIKE :search OR ' +
        'CAST(testCase.priority AS TEXT) LIKE :search OR ' +
        'CAST(testCase.platform AS TEXT) LIKE :search OR ' +
        'CAST(testCase.testCaseType AS TEXT) LIKE :search OR ' +
        'EXISTS (SELECT 1 FROM testcase_tags tt JOIN tags t ON tt."tagId" = t.id WHERE tt."testcaseId" = testCase.id AND t.name LIKE :search))',
        {
          search: `%${search}%`,
          searchNumeric: `%${numericSearch}%`
        }
      );
    }

    // Add folder filter if provided
    if (folderId) {
      queryBuilder.andWhere('testCase.folderId = :folderId', { folderId });
    } else if (folderId === '') {
      // Handle null folder case (root level)
      queryBuilder.andWhere('testCase.folderId IS NULL');
    }

    // Apply advanced filters
    if (filters) {
      // Priority filter
      if (filters.priority && filters.priority.length > 0) {
        queryBuilder.andWhere('testCase.priority IN (:...priorities)', {
          priorities: filters.priority.map(p => p.toLowerCase())
        });
      }

      // Platform filter
      if (filters.platform && filters.platform.length > 0) {
        queryBuilder.andWhere('testCase.platform IN (:...platforms)', {
          platforms: filters.platform.map(p => p.toLowerCase())
        });
      }

      // Test Case Type filter
      if (filters.testCaseType && filters.testCaseType.length > 0) {
        queryBuilder.andWhere('testCase.testCaseType IN (:...testCaseTypes)', {
          testCaseTypes: filters.testCaseType.map(t => t.toLowerCase())
        });
      }

      // Type filter
      if (filters.type && filters.type.length > 0) {
        queryBuilder.andWhere('LOWER(testCase.type) IN (:...types)', {
          types: filters.type.map(t => t.toLowerCase())
        });
      }

      // Tag filter
      if (filters.tagIds && filters.tagIds.length > 0) {
        queryBuilder.andWhere(
          'EXISTS (SELECT 1 FROM testcase_tags tt WHERE tt."testcaseId" = testCase.id AND tt."tagId" IN (:...tagIds))',
          { tagIds: filters.tagIds }
        );
      }
    }

    // Get total count before pagination
    const total = await queryBuilder.getCount();

    // Apply pagination
    const data = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getMany();

    return { data, total };
  }


  async findOne(id: string, projectId: string, userId: string): Promise<TestCase> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    const testCase = await this.testCasesRepository.findOne({
      where: { id, projectId },
      relations: ['tags'],
    });

    if (!testCase) {
      throw new NotFoundException('Test case not found');
    }

    return testCase;
  }

  async findByTcId(tcId: number, projectId: string, userId: string): Promise<TestCase> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    const testCase = await this.testCasesRepository.findOne({
      where: { tcId, projectId },
      relations: ['tags'],
    });

    if (!testCase) {
      throw new NotFoundException(`Test case with tcId ${tcId} not found`);
    }

    return testCase;
  }

  async update(id: string, projectId: string, userId: string, updateTestCaseDto: Partial<CreateTestCaseDto>): Promise<TestCase> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Find the test case
    const testCase = await this.findOne(id, projectId, userId);

    // Handle tags if provided
    if (updateTestCaseDto.tags) {
      const tags = await this.tagsRepository.findBy({ id: In(updateTestCaseDto.tags) });
      if (tags.length !== updateTestCaseDto.tags.length) {
        throw new BadRequestException('One or more tags not found');
      }
      testCase.tags = tags;
      delete updateTestCaseDto.tags; // Remove tags from DTO to avoid overwriting with array of IDs
    }

    // Update other fields
    Object.assign(testCase, updateTestCaseDto);
    const updatedTestCase = await this.testCasesRepository.save(testCase);

    // Emit the test case updated event
    this.eventEmitter.emit('testCase.updated', { projectId: projectId, testCaseId: updatedTestCase.id });

    return updatedTestCase;
  }

  async remove(id: string, projectId: string, userId: string): Promise<void> {
    const testCase = await this.findOne(id, projectId, userId);
    await this.testCasesRepository.remove(testCase);
  }

  async bulkRemove(ids: string[], projectId: string, userId: string): Promise<void> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    await this.testCasesRepository
      .createQueryBuilder()
      .delete()
      .where('id IN (:...ids)', { ids })
      .andWhere('projectId = :projectId', { projectId })
      .execute();
  }

  async createFolder(projectId: string, userId: string, createFolderDto: CreateFolderDto): Promise<TestCaseFolder> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);
    
    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // If parentId is provided, verify it exists and belongs to the same project
    if (createFolderDto.parentId) {
      const parentFolder = await this.foldersRepository.findOne({
        where: { id: createFolderDto.parentId, projectId }
      });
      if (!parentFolder) {
        throw new NotFoundException('Parent folder not found');
      }
    }

    const folder = this.foldersRepository.create({
      ...createFolderDto,
      projectId,
    });

    return this.foldersRepository.save(folder);
  }

  async getFolderStructure(projectId: string, companyId: string) {
    // Verify project exists and belongs to user
    await this.projectsService.findOne(projectId, companyId);

    // Get all folders for the project
    const folders = await this.foldersRepository.find({
      where: { projectId },
      relations: ['children', 'testCases'],
    });

    // Get root level test cases (not in any folder)
    const rootTestCases = await this.testCasesRepository.find({
      where: { projectId, folderId: null },
    });

    // Build tree structure
    const buildTree = (parentId: string | null = null): any[] => {
      const items = [];

      // Add folders
      const childFolders = folders.filter(f => f.parentId === parentId);
      for (const folder of childFolders) {
        items.push({
          id: folder.id,
          name: folder.name,
          type: 'folder',
          children: buildTree(folder.id),
        });
      }

      // Add test cases
      if (parentId === null) {
        items.push(...rootTestCases.map(tc => ({
          id: tc.id,
          name: tc.title,
          type: 'testCase',
          tcId: tc.tcId,
        })));
      } else {
        const folderTestCases = folders
          .find(f => f.id === parentId)
          ?.testCases.map(tc => ({
            id: tc.id,
            name: tc.title,
            type: 'testCase',
            tcId: tc.tcId,
          })) || [];
        items.push(...folderTestCases);
      }

      return items;
    };

    return buildTree();
  }

  async getFolders(projectId: string, companyId: string): Promise<TestCaseFolder[]> {
    // Verify project exists and belongs to user
    await this.projectsService.findOne(projectId, companyId);

    // Get all folders for the project
    const folders = await this.foldersRepository.find({
      where: { projectId },
      order: {
        createdAt: 'ASC'
      }
    });

    // Build folder tree
    const buildTree = (parentId: string | null = null): TestCaseFolder[] => {
      const children = folders.filter(f => f.parentId === parentId);
      return children.map(folder => ({
        ...folder,
        children: buildTree(folder.id)
      }));
    };

    return buildTree(null);
  }

  async getFilterOptions(projectId: string, userId: string): Promise<{
    types: string[];
    tags: Tag[];
  }> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Get all unique types
    const typesResult = await this.testCasesRepository
      .createQueryBuilder('testCase')
      .select('DISTINCT testCase.type', 'type')
      .where('testCase.projectId = :projectId', { projectId })
      .andWhere('testCase.type IS NOT NULL')
      .getRawMany();

    const types = typesResult.map(result => result.type).filter(Boolean);

    // Get all tags used in the project
    const tags = await this.tagsRepository
      .createQueryBuilder('tag')
      .innerJoin('tag.testCases', 'testCase')
      .where('testCase.projectId = :projectId', { projectId })
      .getMany();

    return { types, tags };
  }

  async deleteFolder(projectId: string, folderId: string, userId: string): Promise<void> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    const folder = await this.foldersRepository.findOne({
      where: { id: folderId, projectId },
    });

    if (!folder) {
      throw new NotFoundException('Folder not found');
    }

    // Delete folder and all its contents (handled by cascade)
    await this.foldersRepository.remove(folder);
  }

  async moveItem(projectId: string, userId: string, moveItemDto: MoveItemDto): Promise<void> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Verify target folder if provided
    if (moveItemDto.targetFolderId) {
      const targetFolder = await this.foldersRepository.findOne({
        where: { id: moveItemDto.targetFolderId, projectId },
      });
      if (!targetFolder) {
        throw new NotFoundException('Target folder not found');
      }
    }

    if (moveItemDto.itemType === 'folder') {
      // Move folder
      const folder = await this.foldersRepository.findOne({
        where: { id: moveItemDto.itemId, projectId },
      });
      if (!folder) {
        throw new NotFoundException('Folder not found');
      }

      // Prevent moving folder into itself or its children
      if (moveItemDto.targetFolderId) {
        let currentFolder = await this.foldersRepository.findOne({
          where: { id: moveItemDto.targetFolderId },
        });
        while (currentFolder) {
          if (currentFolder.id === folder.id) {
            throw new BadRequestException('Cannot move folder into itself or its children');
          }
          currentFolder = currentFolder.parentId ? await this.foldersRepository.findOne({
            where: { id: currentFolder.parentId },
          }) : null;
        }
      }

      folder.parentId = moveItemDto.targetFolderId || null;
      await this.foldersRepository.save(folder);
    } else {
      // Move test case
      const testCase = await this.testCasesRepository.findOne({
        where: { id: moveItemDto.itemId, projectId },
      });
      if (!testCase) {
        throw new NotFoundException('Test case not found');
      }

      testCase.folderId = moveItemDto.targetFolderId || null;
      await this.testCasesRepository.save(testCase);
    }
  }

  private async getFolderPath(folder: TestCaseFolder): Promise<string> {
    const path: string[] = [folder.name];
    let currentFolder = folder;

    while (currentFolder.parentId) {
      currentFolder = await this.foldersRepository.findOne({
        where: { id: currentFolder.parentId }
      });
      path.unshift(currentFolder.name);
    }

    return path.join('/');
  }

  async exportToCsv(projectId: string, userId: string): Promise<string> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Get all test cases for the project with relations
    const testCases = await this.testCasesRepository.find({
      where: { projectId },
      order: { tcId: 'ASC' },
      relations: ['tags', 'folder'],
    });

    // CSV header
    const headers = [
      'TC ID',
      'Title',
      'Type',
      'Platform',
      'Priority',
      'Test Case Type',
      'Precondition',
      'Steps',
      'Expected Results',
      'Folder',
      'Tags'
    ].join(',');

    // Convert test cases to CSV rows
    const rows = await Promise.all(testCases.map(async tc => {
      let folderPath = '';
      if (tc.folder) {
        folderPath = await this.getFolderPath(tc.folder);
      }

      return [
        tc.tcId,
        `"${tc.title.replace(/"/g, '""')}"`,
        `"${tc.type}"`,
        tc.platform,
        tc.priority,
        tc.testCaseType,
        `"${tc.precondition ? tc.precondition.replace(/"/g, '""') : ''}"`,
        `"${tc.steps.replace(/"/g, '""')}"`,
        `"${tc.expectation.replace(/"/g, '""')}"`,
        `"${folderPath}"`,
        `"${tc.tags.map(tag => tag.name).join(',')}"`
      ].join(',');
    }));

    // Combine header and rows
    return [headers, ...rows].join('\n');
  }

  private async createFolderHierarchy(projectId: string, folderPath: string): Promise<string> {
    const folderNames = folderPath.split('/').map(name => name.trim());
    let parentId: string | null = null;
    let currentFolderId: string | null = null;

    for (const folderName of folderNames) {
      // Check if folder exists at current level
      let folder = await this.foldersRepository.findOne({
        where: {
          name: folderName,
          projectId,
          parentId: parentId
        }
      });

      if (!folder) {
        // Create new folder
        folder = await this.foldersRepository.save({
          name: folderName,
          projectId,
          parentId
        });
      }

      parentId = folder.id;
      currentFolderId = folder.id;
    }

    return currentFolderId;
  }

  async importFromCsv(projectId: string, userId: string, csvContent: string): Promise<{ imported: number; errors: string[] }> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    const errors: string[] = [];
    let imported = 0;

    try {
      // Parse CSV content
      const records = parse(csvContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      });

      // Get the next tcId for this project
      const maxTcId = await this.testCasesRepository
        .createQueryBuilder('test_case')
        .where('test_case.projectId = :projectId', { projectId })
        .select('MAX(test_case.tcId)', 'max')
        .getRawOne();

      let nextTcId = (maxTcId?.max || 0) + 1;

      // Process each record
      for (const record of records) {
        try {
          // Handle folder creation if specified
          let folderId: string | null = null;
          if (record['Folder']) {
            const folderPath = record['Folder'].trim();
            folderId = await this.createFolderHierarchy(projectId, folderPath);
          }

          // Handle tags if present
          let tags: Tag[] = [];
          if (record['Tags']) {
            const tagNames = record['Tags'].split(',').map((t: string) => t.trim()).filter(Boolean);

            // Process each tag
            for (const tagName of tagNames) {
              let tag = await this.tagsRepository.findOne({
                where: { name: tagName }
              });

              if (!tag) {
                // Create new tag
                tag = await this.tagsRepository.save({
                  name: tagName
                });
              }
              tags.push(tag);
            }
          }

          // Create test case entity
          const testCase = new TestCase();
          Object.assign(testCase, {
            projectId,
            tcId: nextTcId++,
            title: record['Title'],
            type: record['Type'],
            platform: this.validateEnum(record['Platform'], Platform),
            priority: this.validateEnum(record['Priority'], Priority),
            testCaseType: this.validateEnum(record['Test Case Type'], TestCaseType),
            precondition: record['Precondition'],
            steps: record['Steps'],
            expectation: record['Expected Results'],
            folderId,
            tags
          });

          await this.testCasesRepository.save(testCase);
          imported++;
        } catch (error) {
          if (error instanceof Error) {
            errors.push(`Row ${imported + 1}: ${error.message}`);
          } else {
            errors.push(`Row ${imported + 1}: ${error}`);
          }
        }
      }
    } catch (error) {
      throw new BadRequestException('Invalid CSV format');
    }

    // Emit event to notify folder structure changes
    this.eventEmitter.emit('testCase.created', { projectId });

    return { imported, errors };
  }

  private validateEnum<T>(value: string, enumType: T): T[keyof T] {
    if (!value) {
      return null;
    }

    const enumValues = Object.values(enumType);

    // Case-insensitive check
    const normalizedValue = value.toLowerCase();
    const matchingEnumValue = enumValues.find(enumValue =>
      typeof enumValue === 'string' && enumValue.toLowerCase() === normalizedValue
    );

    if (matchingEnumValue) {
      // Return the properly cased enum value from the enum
      return matchingEnumValue as T[keyof T];
    }

    throw new Error(`Invalid value: ${value}. Expected one of: ${enumValues.join(', ')}`);
  }

  /**
   * Start an asynchronous import job
   */
  async startImportJob(projectId: string, userId: string, file: { buffer: Buffer; originalname: string }): Promise<ImportJob> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Create a temporary file to store the CSV content
    const tempDir = os.tmpdir();
    const fileName = `import_${Date.now()}_${Math.random().toString(36).substring(2, 15)}.csv`;
    const filePath = path.join(tempDir, fileName);

    // Write the file to disk
    fs.writeFileSync(filePath, file.buffer);

    // Create an import job record
    const importJob = this.importJobRepository.create({
      fileName: filePath,
      originalFileName: file.originalname,
      status: ImportJobStatus.PENDING,
      progress: 0,
      message: 'Import job created, waiting to start processing',
      projectId: projectId,
    });

    const savedJob = await this.importJobRepository.save(importJob);

    // Start processing the file asynchronously
    this.processImportJob(savedJob.id, projectId, userId).catch(error => {
      console.error(`Error processing import job ${savedJob.id}:`, error);
    });

    return savedJob;
  }

  /**
   * Get the status of an import job
   */
  async getImportJobStatus(projectId: string, jobId: string, userId: string): Promise<ImportJob> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Find the import job
    const importJob = await this.importJobRepository.findOne({
      where: { id: jobId, projectId }
    });

    if (!importJob) {
      throw new NotFoundException(`Import job with ID ${jobId} not found`);
    }

    return importJob;
  }

  /**
   * Process an import job asynchronously
   */
  private async processImportJob(jobId: string, projectId: string, _userId: string): Promise<void> {
    try {
      // Get the import job
      const importJob = await this.importJobRepository.findOne({
        where: { id: jobId }
      });

      if (!importJob) {
        throw new Error(`Import job with ID ${jobId} not found`);
      }

      // Update job status to processing
      importJob.status = ImportJobStatus.PROCESSING;
      importJob.message = 'Processing CSV file';
      await this.importJobRepository.save(importJob);

      // Read the CSV file
      const csvContent = fs.readFileSync(importJob.fileName, 'utf-8');

      // Parse CSV content
      const records = parse(csvContent, {
        columns: true,
        skip_empty_lines: true,
        trim: true,
      });

      // Update job with total rows
      importJob.totalRows = records.length;
      await this.importJobRepository.save(importJob);

      // Get the next tcId for this project
      const maxTcId = await this.testCasesRepository
        .createQueryBuilder('test_case')
        .where('test_case.projectId = :projectId', { projectId })
        .select('MAX(test_case.tcId)', 'max')
        .getRawOne();

      let nextTcId = (maxTcId?.max || 0) + 1;

      // Process each record
      for (let i = 0; i < records.length; i++) {
        const record = records[i];
        try {
          // Handle folder creation if specified
          let folderId: string | null = null;
          if (record['Folder']) {
            const folderPath = record['Folder'].trim();
            folderId = await this.createFolderHierarchy(projectId, folderPath);
          }

          // Handle tags if present
          let tags: Tag[] = [];
          if (record['Tags']) {
            const tagNames = record['Tags'].split(',').map((t: string) => t.trim()).filter(Boolean);

            // Process each tag
            for (const tagName of tagNames) {
              let tag = await this.tagsRepository.findOne({
                where: { name: tagName }
              });

              if (!tag) {
                // Create new tag
                tag = await this.tagsRepository.save({
                  name: tagName
                });
              }
              tags.push(tag);
            }
          }

          // Create test case entity
          const testCase = new TestCase();
          Object.assign(testCase, {
            projectId,
            tcId: nextTcId++,
            title: record['Title'],
            type: record['Type'],
            platform: this.validateEnum(record['Platform'], Platform),
            priority: this.validateEnum(record['Priority'], Priority),
            testCaseType: this.validateEnum(record['Test Case Type'], TestCaseType),
            precondition: record['Precondition'],
            steps: record['Steps'],
            expectation: record['Expected Results'],
            folderId,
            tags
          });

          await this.testCasesRepository.save(testCase);

          // Update job progress
          importJob.processedRows = i + 1;
          importJob.successRows += 1;
          importJob.progress = (i + 1) / records.length;
          importJob.message = `Processed ${i + 1} of ${records.length} records`;

          // Save progress every 10 records or on the last record
          if ((i + 1) % 10 === 0 || i === records.length - 1) {
            await this.importJobRepository.save(importJob);
          }
        } catch (error) {
          // Log the error and continue with the next record
          importJob.errorRows += 1;
          importJob.processedRows = i + 1;
          importJob.progress = (i + 1) / records.length;
          importJob.message = `Processed ${i + 1} of ${records.length} records (with errors)`;

          // Save progress every 10 records or on the last record
          if ((i + 1) % 10 === 0 || i === records.length - 1) {
            await this.importJobRepository.save(importJob);
          }
        }
      }

      // Update job status to completed
      importJob.status = ImportJobStatus.COMPLETED;
      importJob.progress = 1;
      importJob.message = `Import completed. Successfully imported ${importJob.successRows} of ${importJob.totalRows} records.`;
      await this.importJobRepository.save(importJob);

      // Emit event to notify folder structure changes
      this.eventEmitter.emit('testCase.created', { projectId });

      // Clean up the temporary file
      try {
        fs.unlinkSync(importJob.fileName);
      } catch (error) {
        console.error(`Error deleting temporary file ${importJob.fileName}:`, error);
      }
    } catch (error) {
      // Update job status to error
      const importJob = await this.importJobRepository.findOne({
        where: { id: jobId }
      });

      if (importJob) {
        importJob.status = ImportJobStatus.ERROR;
        importJob.error = error instanceof Error ? error.message : 'Unknown error occurred during import';
        importJob.message = 'Import failed';
        await this.importJobRepository.save(importJob);

        // Clean up the temporary file
        try {
          fs.unlinkSync(importJob.fileName);
        } catch (fileError) {
          console.error(`Error deleting temporary file ${importJob.fileName}:`, fileError);
        }
      }
    }
  }

  // Helper method to get user with companyId, name, and email
  private async getUserWithCompanyId(userId: string): Promise<{ id: string; companyId: string; name?: string; email?: string }> {
    const user = await this.usersRepository.findOne({ where: { id: userId } });
    
    if (!user || !user.companyId) {
      throw new NotFoundException(`User with ID ${userId} not found or has no company`);
    }
    
    return { id: user.id, companyId: user.companyId, name: user.name, email: user.email };
  }

  /**
   * Save or update automation steps for a test case
   */
  async saveAutomationSteps(projectId: string, tcId: string, automationDto: AutomationTestCaseDto, userId: string): Promise<AutomationTestCase> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Find the test case by tcId
    const testCase = await this.testCasesRepository.findOne({
      where: { projectId, tcId: parseInt(tcId) }
    });

    if (!testCase) {
      throw new NotFoundException(`Test case with tcId ${tcId} not found in project ${projectId}`);
    }

    // Check if automation already exists for this test case
    let automationTestCase = await this.automationTestCasesRepository.findOne({
      where: { testCaseId: testCase.id }
    });

    if (automationTestCase) {
      // Update existing automation
      automationTestCase.steps = automationDto.steps;
      return this.automationTestCasesRepository.save(automationTestCase);
    } else {
      // Create new automation
      automationTestCase = new AutomationTestCase();
      automationTestCase.testCaseId = testCase.id;
      automationTestCase.steps = automationDto.steps;
      return this.automationTestCasesRepository.save(automationTestCase);
    }
  }

  /**
   * Get automation steps for a test case
   */
  async getAutomationSteps(projectId: string, tcId: string, userId: string): Promise<AutomationTestCase | null> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Find the test case by tcId
    const testCase = await this.testCasesRepository.findOne({
      where: { projectId, tcId: parseInt(tcId) }
    });

    if (!testCase) {
      throw new NotFoundException(`Test case with tcId ${tcId} not found in project ${projectId}`);
    }

    // Find automation for this test case
    const automationTestCase = await this.automationTestCasesRepository.findOne({
      where: { testCaseId: testCase.id }
    });

    return automationTestCase;
  }

  /**
   * Find a test case by ID without permission checks
   * This is used internally by other services
   * @param id - The test case ID
   * @returns Promise<TestCase>
   */
  async findOneBasic(id: string): Promise<TestCase> {
    const testCase = await this.testCasesRepository.findOne({
      where: { id },
      relations: ['tags'],
    });

    if (!testCase) {
      throw new NotFoundException(`Test case with ID ${id} not found`);
    }

    return testCase;
  }

  async updateTestCaseType(
    projectId: string,
    tcId: number,
    testCaseType: TestCaseType,
    userId: string
  ): Promise<TestCase> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Find the test case
    const testCase = await this.testCasesRepository.findOne({
      where: { projectId, tcId },
      relations: ['tags']
    });

    if (!testCase) {
      throw new NotFoundException(`Test case with tcId ${tcId} not found in project ${projectId}`);
    }

    // Update test case type
    testCase.testCaseType = testCaseType;
    const updatedTestCase = await this.testCasesRepository.save(testCase);

    // Emit the test case updated event
    this.eventEmitter.emit('testCase.updated', { 
      projectId: projectId, 
      testCaseId: updatedTestCase.id 
    });

    return updatedTestCase;
  }

  async updateAutomationByAgentq(
    projectId: string,
    tcId: number,
    automationByAgentq: boolean,
    userId: string
  ): Promise<TestCase> {
    // Get user's companyId
    const user = await this.getUserWithCompanyId(userId);

    // Verify project exists and belongs to user's company
    await this.projectsService.findOne(projectId, user.companyId);

    // Find the test case
    const testCase = await this.testCasesRepository.findOne({
      where: { projectId, tcId },
      relations: ['tags']
    });

    if (!testCase) {
      throw new NotFoundException(`Test case with tcId ${tcId} not found in project ${projectId}`);
    }

    // Update automation flag
    testCase.automationByAgentq = automationByAgentq;
    const updatedTestCase = await this.testCasesRepository.save(testCase);

    // Emit the test case updated event
    this.eventEmitter.emit('testCase.updated', { 
      projectId: projectId, 
      testCaseId: updatedTestCase.id 
    });

    return updatedTestCase;
  }

  /**
   * Fetch ZAP security report from stored CLIENT_ID-isolated reports
   * CRITICAL: This method now ONLY fetches from stored reports to prevent cross-contamination
   */
  async fetchZapReport(userId: string, testCaseId?: string): Promise<any> {
    console.log('🔍 Starting fetchZapReport for userId:', userId, 'testCaseId:', testCaseId);

    try {
      // Simple authorization check - just verify user exists
      if (!userId) {
        console.error('❌ No userId provided');
        throw new BadRequestException('User ID is required');
      }

      // CRITICAL: Only fetch from stored reports to ensure CLIENT_ID isolation
      // Direct ZAP fetching is disabled to prevent cross-contamination
      if (testCaseId) {
        const storedReport = await this.getStoredSecurityReport(testCaseId);
        if (storedReport) {
          console.log('✅ Found CLIENT_ID-isolated stored security report');
          return storedReport;
        } else {
          console.log('⚠️ No stored security report found for test case:', testCaseId);
          return {
            success: false,
            message: 'No security report available. Please run a security test first.',
            data: null
          };
        }
      }

      // If no testCaseId provided, return error
      console.log('❌ No testCaseId provided - cannot fetch report without test case context');
      return {
        success: false,
        message: 'Test case ID is required to fetch security report',
        data: null
      };

    } catch (error: any) {
      console.error('❌ Error fetching stored ZAP report:', error.message);
      throw new BadRequestException(`Failed to fetch security report: ${error.message}`);
    }
  }

  /**
   * Parse vulnerabilities from ZAP HTML report
   */
  private parseZapVulnerabilities(htmlReport: string): any[] {
    try {
      const vulnerabilities = [];

      // Simple regex-based parsing of HTML report
      // Look for vulnerability entries in the HTML
      const vulnRegex = /<h3[^>]*>([^<]+)<\/h3>[\s\S]*?Risk:\s*([^<\n]+)/gi;
      let match: RegExpExecArray | null;

      while ((match = vulnRegex.exec(htmlReport)) !== null) {
        const name = match[1].trim();
        const risk = match[2].trim();

        vulnerabilities.push({
          name,
          risk,
          description: `Security vulnerability: ${name}`,
          category: 'Security'
        });
      }

      // If no vulnerabilities found with regex, try alternative parsing
      if (vulnerabilities.length === 0) {
        // Look for risk level indicators
        const riskCounts = {
          High: (htmlReport.match(/High/gi) || []).length,
          Medium: (htmlReport.match(/Medium/gi) || []).length,
          Low: (htmlReport.match(/Low/gi) || []).length,
          Informational: (htmlReport.match(/Informational/gi) || []).length
        };

        // Create summary entries if risk levels are found
        Object.entries(riskCounts).forEach(([risk, count]) => {
          if (count > 0) {
            vulnerabilities.push({
              name: `${risk} Risk Issues`,
              risk,
              description: `${count} ${risk.toLowerCase()} risk issues found`,
              category: 'Summary',
              count
            });
          }
        });
      }

      return vulnerabilities;
    } catch (error) {
      console.error('Error parsing ZAP vulnerabilities:', error);
      return [];
    }
  }

  /**
   * Fetch scan context information from ZAP
   */
  private async fetchZapScanContext(): Promise<any> {
    const scanContext: any = {
      sites: [],
      urls: [],
      hosts: [],
      scanStartTime: new Date().toISOString(),
      scanEndTime: new Date().toISOString(),
      targetUrl: null
    };

    try {
      // Fetch sites that were accessed
      const sitesResponse = await axios.get(
        'http://localhost:8080/JSON/core/view/sites/?apikey=AgentqSuperAI',
        { timeout: 5000 }
      );
      if (sitesResponse.data && sitesResponse.data.sites) {
        scanContext.sites = sitesResponse.data.sites;
      }
    } catch (error) {
      console.warn('Error fetching ZAP sites:', error);
    }

    try {
      // Fetch URLs that were accessed
      const urlsResponse = await axios.get(
        'http://localhost:8080/JSON/core/view/urls/?apikey=AgentqSuperAI',
        { timeout: 5000 }
      );
      if (urlsResponse.data && urlsResponse.data.urls) {
        scanContext.urls = urlsResponse.data.urls;
      }
    } catch (error) {
      console.warn('Error fetching ZAP URLs:', error);
    }

    try {
      // Fetch hosts that were accessed
      const hostsResponse = await axios.get(
        'http://localhost:8080/JSON/core/view/hosts/?apikey=AgentqSuperAI',
        { timeout: 5000 }
      );
      if (hostsResponse.data && hostsResponse.data.hosts) {
        scanContext.hosts = hostsResponse.data.hosts;
      }
    } catch (error) {
      console.warn('Error fetching ZAP hosts:', error);
    }

    // Set target URL from the first site if available
    if (scanContext.sites.length > 0) {
      scanContext.targetUrl = scanContext.sites[0];
    }

    console.log(`📊 ZAP scan context: ${scanContext.sites.length} sites, ${scanContext.urls.length} URLs, ${scanContext.hosts.length} hosts`);

    return scanContext;
  }

  /**
   * Get stored security report from temp test results
   */
  private async getStoredSecurityReport(testCaseId: string): Promise<any> {
    try {
      // Find the latest temp test result for this test case with security report
      const tempTestResult = await this.tempTestResultRepository.findOne({
        where: {
          testCaseId,
          logsSecurityUrl: Not(IsNull())
        },
        order: { createdAt: 'DESC' }
      });

      if (tempTestResult && tempTestResult.logsSecurityUrl) {
        console.log('📋 Found stored security report:', tempTestResult.logsSecurityUrl);

        // Check if we have detailed security report data stored in database
        if (tempTestResult.securityReportData) {
          console.log('📊 Using detailed security report data from database');
          const reportData = tempTestResult.securityReportData;

          // Return detailed vulnerability data from database
          return {
            success: true,
            data: {
              stored: true,
              reportUrl: tempTestResult.logsSecurityUrl,
              timestamp: tempTestResult.createdAt,
              summary: reportData.summary || {
                message: 'Security report available from previous scan',
                storedAt: tempTestResult.createdAt,
                totalIssues: reportData.vulnerabilities?.length || 0,
                highRisk: reportData.vulnerabilities?.filter((v: any) => v.risk === 'High').length || 0,
                mediumRisk: reportData.vulnerabilities?.filter((v: any) => v.risk === 'Medium').length || 0,
                lowRisk: reportData.vulnerabilities?.filter((v: any) => v.risk === 'Low').length || 0,
                informational: reportData.vulnerabilities?.filter((v: any) => v.risk === 'Informational').length || 0,
                urlsScanned: reportData.scanContext?.urlsScanned || reportData.scanContext?.urls?.length || 0
              },
              vulnerabilities: reportData.vulnerabilities || [],
              scanContext: reportData.scanContext || null
            }
          };
        } else {
          // Try to fetch the detailed report data from the stored location (fallback)
          const detailedReport = await this.fetchStoredReportDetails(tempTestResult.logsSecurityUrl);

          if (detailedReport) {
            // Return detailed vulnerability data if available
            return {
              success: true,
              data: {
                stored: true,
                reportUrl: tempTestResult.logsSecurityUrl,
                timestamp: tempTestResult.createdAt,
                summary: detailedReport.summary || {
                  message: 'Security report available from previous scan',
                  storedAt: tempTestResult.createdAt,
                  totalIssues: detailedReport.vulnerabilities?.length || 0,
                  highRisk: detailedReport.vulnerabilities?.filter((v: any) => v.risk === 'High').length || 0,
                  mediumRisk: detailedReport.vulnerabilities?.filter((v: any) => v.risk === 'Medium').length || 0,
                  lowRisk: detailedReport.vulnerabilities?.filter((v: any) => v.risk === 'Low').length || 0,
                  informational: detailedReport.vulnerabilities?.filter((v: any) => v.risk === 'Informational').length || 0,
                  urlsScanned: detailedReport.scanContext?.urlsScanned || detailedReport.scanContext?.urls?.length || 0
                },
                vulnerabilities: detailedReport.vulnerabilities || [],
                scanContext: detailedReport.scanContext || null
              }
            };
          } else {
            // Fallback to basic stored report info
            return {
              success: true,
              data: {
                stored: true,
                reportUrl: tempTestResult.logsSecurityUrl,
                timestamp: tempTestResult.createdAt,
                summary: {
                  message: 'Security report available from previous scan',
                  storedAt: tempTestResult.createdAt
                }
              }
            };
          }
        }
      }

      return null;
    } catch (error) {
      console.error('Error getting stored security report:', error);
      return null;
    }
  }

  /**
   * Fetch detailed report data from stored location
   */
  private async fetchStoredReportDetails(reportUrl: string): Promise<any> {
    try {
      // Check if it's a local file path or Google Cloud Storage URL
      if (reportUrl.startsWith('gs://')) {
        // For Google Cloud Storage, we would need to implement GCS client
        // For now, return null to use fallback
        console.log('📊 GCS report URL detected, detailed parsing not implemented yet');
        return null;
      } else if (reportUrl.startsWith('local://')) {
        // Handle local file storage
        const fs = require('fs');
        const path = require('path');

        // Extract local file path
        const localPath = reportUrl.replace('local://', '');
        const fullPath = path.resolve(localPath);

        if (fs.existsSync(fullPath)) {
          const reportData = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
          console.log('📊 Loaded detailed report from local storage');
          return reportData;
        } else {
          console.warn('⚠️ Local report file not found:', fullPath);
          return null;
        }
      } else {
        // Assume it's a direct file path
        const fs = require('fs');
        const path = require('path');

        if (fs.existsSync(reportUrl)) {
          const reportData = JSON.parse(fs.readFileSync(reportUrl, 'utf8'));
          console.log('📊 Loaded detailed report from file path');
          return reportData;
        } else {
          console.warn('⚠️ Report file not found:', reportUrl);
          return null;
        }
      }
    } catch (error) {
      console.error('❌ Error fetching stored report details:', error);
      return null;
    }
  }

  /**
   * Save security report URL to temp test results
   */
  async saveSecurityReportUrl(testCaseId: string, securityReportUrl: string): Promise<void> {
    try {
      console.log('💾 Saving security report URL for test case:', testCaseId);

      // Find the latest temp test result for this test case
      const tempTestResult = await this.tempTestResultRepository.findOne({
        where: { testCaseId },
        order: { createdAt: 'DESC' }
      });

      if (tempTestResult) {
        tempTestResult.logsSecurityUrl = securityReportUrl;
        await this.tempTestResultRepository.save(tempTestResult);
        console.log('✅ Security report URL saved successfully');
      } else {
        console.warn('⚠️ No temp test result found for test case:', testCaseId);
      }
    } catch (error) {
      console.error('❌ Error saving security report URL:', error);
    }
  }

}

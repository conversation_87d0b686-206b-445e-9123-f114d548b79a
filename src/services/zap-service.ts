import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

export interface ZapReportData {
  html: string;
  json?: any;
  timestamp: string;
  testCaseId: string;
  clientId: string;
  vulnerabilities: ZapVulnerability[];
  summary: ZapSummary;
  scanContext: ZapScanContext;
}

export interface ZapVulnerability {
  name: string;
  risk: 'High' | 'Medium' | 'Low' | 'Informational';
  confidence: string;
  description: string;
  solution?: string;
  reference?: string;
  instances: ZapInstance[];
}

export interface ZapInstance {
  uri: string;
  method: string;
  param?: string;
  evidence?: string;
}

export interface ZapSummary {
  totalIssues: number;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
  informational: number;
  scanDuration?: number;
  urlsScanned?: number;
}

export interface ZapScanContext {
  sites: string[];
  urls: string[];
  hosts: string[];
  targetUrl?: string;
  scanStartTime?: string;
  scanEndTime?: string;
  spiderResults?: any;
  activeScanResults?: any;
}

export class ZapService {
  private static readonly ZAP_HOST = process.env.ZAP_HOST || 'http://localhost:8080';
  private static readonly ZAP_API_KEY = process.env.ZAP_API_KEY || 'AgentqSuperAI';
  private static readonly BACKEND_URL = process.env.AGENTQ_API_URL || process.env.BACKEND_URL || 'http://localhost:3010';

  // Track active ZAP sessions by CLIENT_ID to prevent cross-contamination
  private static activeSessions: Map<string, string> = new Map();

  // ZAP operation queue to prevent concurrent scans from interfering
  private static zapOperationQueue: Array<() => Promise<any>> = [];
  private static isZapBusy = false;

  /**
   * Queue ZAP operations to prevent concurrent scan interference
   * This ensures only one ZAP operation runs at a time
   */
  private static async queueZapOperation<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.zapOperationQueue.push(async () => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processZapQueue();
    });
  }

  /**
   * Process the ZAP operation queue
   */
  private static async processZapQueue(): Promise<void> {
    if (this.isZapBusy || this.zapOperationQueue.length === 0) {
      return;
    }

    this.isZapBusy = true;
    const operation = this.zapOperationQueue.shift();

    if (operation) {
      try {
        await operation();
      } catch (error) {
        console.error('ZAP operation failed:', error);
      }
    }

    this.isZapBusy = false;

    // Process next operation if any
    if (this.zapOperationQueue.length > 0) {
      setTimeout(() => this.processZapQueue(), 100); // Small delay between operations
    }
  }

  /**
   * Create or get ZAP session for CLIENT_ID isolation
   * This prevents reports from different users/companies from mixing
   */
  private static async ensureClientSession(clientId: string): Promise<string> {
    try {
      // Check if we already have a session for this client
      let sessionName = this.activeSessions.get(clientId);

      if (!sessionName) {
        // Create new session for this client
        sessionName = `session_${clientId}_${Date.now()}`;

        // Create new session in ZAP
        const response = await axios.get(
          `${this.ZAP_HOST}/JSON/core/action/newSession/?apikey=${this.ZAP_API_KEY}&name=${sessionName}&overwrite=true`
        );

        if (response.data.Result === 'OK') {
          this.activeSessions.set(clientId, sessionName);
          console.log(`🔒 Created ZAP session for CLIENT_ID ${clientId}: ${sessionName}`);
        } else {
          console.warn(`⚠️ Failed to create ZAP session for CLIENT_ID ${clientId}`);
          sessionName = 'default';
        }
      }

      return sessionName;
    } catch (error) {
      console.error(`❌ Error managing ZAP session for CLIENT_ID ${clientId}:`, error);
      return 'default';
    }
  }

  /**
   * Clean up ZAP session for a client
   */
  private static async cleanupClientSession(clientId: string): Promise<void> {
    try {
      const sessionName = this.activeSessions.get(clientId);
      if (sessionName) {
        // Remove session from ZAP
        await axios.get(
          `${this.ZAP_HOST}/JSON/core/action/removeSession/?apikey=${this.ZAP_API_KEY}&session=${sessionName}`
        );

        this.activeSessions.delete(clientId);
        console.log(`🧹 Cleaned up ZAP session for CLIENT_ID ${clientId}: ${sessionName}`);
      }
    } catch (error) {
      console.error(`❌ Error cleaning up ZAP session for CLIENT_ID ${clientId}:`, error);
    }
  }

  /**
   * Generate ZAP security report for a completed test
   * @param clientId - Unique client ID for isolation
   * @param testCaseId - Test case ID
   * @returns Promise<ZapReportData | null>
   */
  static async generateSecurityReport(clientId: string, testCaseId: string): Promise<ZapReportData | null> {
    // Queue this ZAP operation to prevent concurrent scan interference
    return this.queueZapOperation(async () => {
      try {
        console.log(`🔍 Generating ZAP security report for client: ${clientId}, test case: ${testCaseId} (queued)`);

        // CRITICAL: Ensure CLIENT_ID session isolation before generating report
        const sessionName = await this.ensureClientSession(clientId);
        console.log(`🔒 Using ZAP session: ${sessionName} for CLIENT_ID: ${clientId}`);

      // Get HTML report from ZAP (session-isolated)
      const htmlReport = await this.fetchZapHtmlReport(sessionName);
      if (!htmlReport) {
        console.warn('⚠️ No ZAP HTML report available');
        return null;
      }

      // Get JSON report from ZAP for detailed analysis (session-isolated)
      const jsonReport = await this.fetchZapJsonReport(sessionName);

      // Get scan context information (session-isolated)
      console.log('🔍 Fetching ZAP scan context...');
      const scanContext = await this.fetchZapScanContext(sessionName);
      console.log(`✅ Scan context fetched: ${scanContext.sites.length} sites, ${scanContext.urls.length} URLs`);

      // Parse vulnerabilities from the reports
      const vulnerabilities = this.parseVulnerabilities(htmlReport, jsonReport);
      const summary = this.generateSummary(vulnerabilities, scanContext);

      const reportData: ZapReportData = {
        html: htmlReport,
        json: jsonReport,
        timestamp: new Date().toISOString(),
        testCaseId,
        clientId,
        vulnerabilities,
        summary,
        scanContext
      };

      // Save report to client-isolated directory
      const reportPath = await this.saveReportToFile(reportData);
      console.log(`📊 ZAP report saved to: ${reportPath}`);

      // Store report URL in backend database
      await this.storeReportInDatabase(testCaseId, reportPath, reportData);

      // Clean up ZAP session after report generation to prevent buildup
      await this.cleanupClientSession(clientId);

        return reportData;
      } catch (error) {
        console.error('❌ Error generating ZAP security report:', error);
        // Clean up session even on error
        await this.cleanupClientSession(clientId);
        return null;
      }
    });
  }

  /**
   * Fetch HTML report from ZAP (session-isolated)
   */
  private static async fetchZapHtmlReport(sessionName?: string): Promise<string | null> {
    try {
      let url = `${this.ZAP_HOST}/OTHER/core/other/htmlreport/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        url += `&session=${sessionName}`;
      }

      const response = await axios.get(url, { timeout: 10000 });
      return response.data;
    } catch (error) {
      console.error('Error fetching ZAP HTML report:', error);
      return null;
    }
  }

  /**
   * Fetch JSON report from ZAP for detailed analysis (session-isolated)
   */
  private static async fetchZapJsonReport(sessionName?: string): Promise<any | null> {
    try {
      let url = `${this.ZAP_HOST}/JSON/core/view/alerts/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        url += `&session=${sessionName}`;
      }

      const response = await axios.get(url, { timeout: 10000 });
      return response.data;
    } catch (error) {
      console.error('Error fetching ZAP JSON report:', error);
      return null;
    }
  }

  /**
   * Fetch scan context information from ZAP (session-isolated)
   */
  private static async fetchZapScanContext(sessionName?: string): Promise<ZapScanContext> {
    const scanContext: ZapScanContext = {
      sites: [],
      urls: [],
      hosts: [],
      scanStartTime: new Date().toISOString(),
      scanEndTime: new Date().toISOString()
    };

    try {
      // Fetch sites that were accessed (session-isolated)
      let sitesUrl = `${this.ZAP_HOST}/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        sitesUrl += `&session=${sessionName}`;
      }
      const sitesResponse = await axios.get(sitesUrl, { timeout: 5000 });
      if (sitesResponse.data && sitesResponse.data.sites) {
        scanContext.sites = sitesResponse.data.sites;
      }
    } catch (error) {
      console.warn('Error fetching ZAP sites:', error);
    }

    try {
      // Fetch URLs that were accessed (session-isolated)
      let urlsUrl = `${this.ZAP_HOST}/JSON/core/view/urls/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        urlsUrl += `&session=${sessionName}`;
      }
      const urlsResponse = await axios.get(urlsUrl, { timeout: 5000 });
      if (urlsResponse.data && urlsResponse.data.urls) {
        scanContext.urls = urlsResponse.data.urls;
      }
    } catch (error) {
      console.warn('Error fetching ZAP URLs:', error);
    }

    try {
      // Fetch hosts that were accessed (session-isolated)
      let hostsUrl = `${this.ZAP_HOST}/JSON/core/view/hosts/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        hostsUrl += `&session=${sessionName}`;
      }
      const hostsResponse = await axios.get(hostsUrl, { timeout: 5000 });
      if (hostsResponse.data && hostsResponse.data.hosts) {
        scanContext.hosts = hostsResponse.data.hosts;
      }
    } catch (error) {
      console.warn('Error fetching ZAP hosts:', error);
    }

    try {
      // Fetch spider results if available (session-isolated)
      let spiderUrl = `${this.ZAP_HOST}/JSON/spider/view/results/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        spiderUrl += `&session=${sessionName}`;
      }
      const spiderResponse = await axios.get(spiderUrl, { timeout: 5000 });
      if (spiderResponse.data) {
        scanContext.spiderResults = spiderResponse.data;
      }
    } catch (error) {
      console.warn('Error fetching ZAP spider results:', error);
    }

    try {
      // Fetch active scan results if available (session-isolated)
      let ascanUrl = `${this.ZAP_HOST}/JSON/ascan/view/scans/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        ascanUrl += `&session=${sessionName}`;
      }
      const ascanResponse = await axios.get(ascanUrl, { timeout: 5000 });
      if (ascanResponse.data) {
        scanContext.activeScanResults = ascanResponse.data;
      }
    } catch (error) {
      console.warn('Error fetching ZAP active scan results:', error);
    }

    // Set target URL from the first site if available
    if (scanContext.sites.length > 0) {
      scanContext.targetUrl = scanContext.sites[0];
    }

    console.log(`📊 ZAP scan context: ${scanContext.sites.length} sites, ${scanContext.urls.length} URLs, ${scanContext.hosts.length} hosts`);

    return scanContext;
  }

  /**
   * Parse vulnerabilities from ZAP reports
   */
  private static parseVulnerabilities(htmlReport: string, jsonReport?: any): ZapVulnerability[] {
    const vulnerabilities: ZapVulnerability[] = [];

    try {
      // Parse from JSON report if available (more structured)
      if (jsonReport && jsonReport.alerts) {
        jsonReport.alerts.forEach((alert: any) => {
          const vulnerability: ZapVulnerability = {
            name: alert.name || 'Unknown Vulnerability',
            risk: this.mapRiskLevel(alert.risk),
            confidence: alert.confidence || 'Unknown',
            description: alert.description || '',
            solution: alert.solution || '',
            reference: alert.reference || '',
            instances: alert.instances?.map((instance: any) => ({
              uri: instance.uri || '',
              method: instance.method || '',
              param: instance.param || '',
              evidence: instance.evidence || ''
            })) || []
          };
          vulnerabilities.push(vulnerability);
        });
      } else {
        // Fallback to HTML parsing if JSON not available
        const htmlVulns = this.parseHtmlReport(htmlReport);
        vulnerabilities.push(...htmlVulns);
      }
    } catch (error) {
      console.error('Error parsing vulnerabilities:', error);
    }

    return vulnerabilities;
  }

  /**
   * Parse vulnerabilities from HTML report (fallback method)
   */
  private static parseHtmlReport(htmlReport: string): ZapVulnerability[] {
    const vulnerabilities: ZapVulnerability[] = [];

    try {
      // Simple regex-based parsing for HTML report
      const vulnRegex = /<h3[^>]*>([^<]+)<\/h3>[\s\S]*?Risk:\s*([^<\n]+)/gi;
      let match;

      while ((match = vulnRegex.exec(htmlReport)) !== null) {
        const name = match[1].trim();
        const risk = this.mapRiskLevel(match[2].trim());

        vulnerabilities.push({
          name,
          risk,
          confidence: 'Unknown',
          description: `Security vulnerability: ${name}`,
          instances: []
        });
      }
    } catch (error) {
      console.error('Error parsing HTML report:', error);
    }

    return vulnerabilities;
  }

  /**
   * Map ZAP risk levels to standardized format
   */
  private static mapRiskLevel(risk: string): 'High' | 'Medium' | 'Low' | 'Informational' {
    const riskLower = risk.toLowerCase();
    if (riskLower.includes('high')) return 'High';
    if (riskLower.includes('medium')) return 'Medium';
    if (riskLower.includes('low')) return 'Low';
    return 'Informational';
  }

  /**
   * Generate summary statistics from vulnerabilities
   */
  private static generateSummary(vulnerabilities: ZapVulnerability[], scanContext?: ZapScanContext): ZapSummary {
    const summary: ZapSummary = {
      totalIssues: vulnerabilities.length,
      highRisk: vulnerabilities.filter(v => v.risk === 'High').length,
      mediumRisk: vulnerabilities.filter(v => v.risk === 'Medium').length,
      lowRisk: vulnerabilities.filter(v => v.risk === 'Low').length,
      informational: vulnerabilities.filter(v => v.risk === 'Informational').length,
      urlsScanned: scanContext?.urls.length || 0
    };

    return summary;
  }

  /**
   * Save ZAP report to client-isolated file system
   */
  private static async saveReportToFile(reportData: ZapReportData): Promise<string> {
    try {
      // CRITICAL: Use CLIENT_ID from reportData to ensure proper isolation
      // This prevents cross-contamination between different users/companies in SaaS
      const clientId = reportData.clientId || process.env.CLIENT_ID || 'default';
      const outputDir = `test-results/${clientId}`;

      const zapReportsDir = path.join(outputDir, 'zap-reports');

      // Ensure directory exists
      if (!fs.existsSync(zapReportsDir)) {
        fs.mkdirSync(zapReportsDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const reportFileName = `zap-report-${reportData.testCaseId}-${timestamp}.json`;
      const reportPath = path.join(zapReportsDir, reportFileName);

      // Save complete report data as JSON
      fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));

      // Also save HTML report separately for easy viewing
      const htmlFileName = `zap-report-${reportData.testCaseId}-${timestamp}.html`;
      const htmlPath = path.join(zapReportsDir, htmlFileName);
      fs.writeFileSync(htmlPath, reportData.html);

      console.log(`📁 ZAP report saved to CLIENT_ID isolated path: ${reportPath}`);
      console.log(`🔒 CLIENT_ID: ${clientId} - Report isolation ensured`);
      return reportPath;
    } catch (error) {
      console.error('Error saving ZAP report to file:', error);
      throw error;
    }
  }

  /**
   * Store ZAP report reference in backend database
   */
  private static async storeReportInDatabase(testCaseId: string, reportPath: string, reportData: ZapReportData): Promise<void> {
    try {
      console.log(`💾 Storing ZAP report reference in database for test case: ${testCaseId}`);
      console.log(`📊 Report data summary: ${reportData.vulnerabilities.length} vulnerabilities, ${(reportData.scanContext.urls || []).length} URLs scanned`);

      const requestPayload = {
        testCaseId: testCaseId,
        zapReport: {
          reportPath: reportPath,
          summary: reportData.summary,
          vulnerabilities: reportData.vulnerabilities.slice(0, 10), // Store first 10 for quick access
          timestamp: reportData.timestamp,
          clientId: reportData.clientId,
          scanContext: {
            sites: reportData.scanContext.sites || [],
            urls: (reportData.scanContext.urls || []).slice(0, 50), // Store first 50 URLs for quick access
            hosts: reportData.scanContext.hosts || [],
            targetUrl: reportData.scanContext.targetUrl || null,
            urlsScanned: (reportData.scanContext.urls || []).length
          }
        }
      };

      console.log(`📤 Sending request to: ${this.BACKEND_URL}/temp-test-results/security-logs`);

      const response = await axios.post(
        `${this.BACKEND_URL}/temp-test-results/security-logs`,
        requestPayload,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      if (response.data.success) {
        console.log(`✅ ZAP report reference stored in database: ${response.data.data.logsSecurityUrl}`);
      } else {
        console.warn('⚠️ Failed to store ZAP report reference in database:', response.data.message);
      }
    } catch (error: any) {
      console.error('❌ Error storing ZAP report in database:', error);

      if (error.response) {
        console.error(`📤 HTTP ${error.response.status}: ${error.response.statusText}`);
        console.error(`📄 Response data:`, error.response.data);
      } else if (error.request) {
        console.error('📡 No response received from backend:', error.request);
      } else {
        console.error('⚙️ Request setup error:', error.message);
      }

      // Don't throw error - report is still saved locally
    }
  }

  /**
   * Initialize CLIENT_ID-specific ZAP session before starting new test
   * This ensures proper isolation between different users/companies
   */
  static async initializeClientSession(clientId: string): Promise<void> {
    // Queue this ZAP operation to prevent concurrent session initialization
    return this.queueZapOperation(async () => {
      try {
        console.log(`🔧 Initializing ZAP session for CLIENT_ID: ${clientId}... (queued)`);

        // Ensure we have a clean session for this client
        await this.ensureClientSession(clientId);

        console.log(`✅ ZAP session initialized for CLIENT_ID: ${clientId}`);
      } catch (error) {
        console.warn(`⚠️ Failed to initialize ZAP session for CLIENT_ID ${clientId}:`, error);
        // Don't throw error - test can continue
      }
    });
  }

  /**
   * Clear ZAP session before starting new test (legacy method - deprecated)
   * @deprecated Use initializeClientSession instead for proper CLIENT_ID isolation
   */
  static async clearZapSession(): Promise<void> {
    try {
      console.log('🧹 Clearing ZAP session for new test (legacy method)...');

      // Clear ZAP session
      await axios.get(
        `${this.ZAP_HOST}/JSON/core/action/newSession/?apikey=${this.ZAP_API_KEY}`,
        { timeout: 5000 }
      );

      console.log('✅ ZAP session cleared');
    } catch (error) {
      console.warn('⚠️ Failed to clear ZAP session:', error);
      // Don't throw error - test can continue
    }
  }
}

import axios from 'axios';
import * as fs from 'fs';
import * as path from 'path';

export interface ZapReportData {
  html: string;
  json?: any;
  timestamp: string;
  testCaseId: string;
  clientId: string;
  vulnerabilities: ZapVulnerability[];
  summary: ZapSummary;
  scanContext: ZapScanContext;
}

export interface ZapVulnerability {
  name: string;
  risk: 'High' | 'Medium' | 'Low' | 'Informational';
  confidence: string;
  description: string;
  solution?: string;
  reference?: string;
  instances: ZapInstance[];
}

export interface ZapInstance {
  uri: string;
  method: string;
  param?: string;
  evidence?: string;
}

export interface ZapSummary {
  totalIssues: number;
  highRisk: number;
  mediumRisk: number;
  lowRisk: number;
  informational: number;
  scanDuration?: number;
  urlsScanned?: number;
}

export interface ZapScanContext {
  sites: string[];
  urls: string[];
  hosts: string[];
  targetUrl?: string;
  scanStartTime?: string;
  scanEndTime?: string;
  spiderResults?: any;
  activeScanResults?: any;
}

export interface ZapContainerInfo {
  containerId: string;
  containerName: string;
  port: number;
  host: string;
  clientId: string;
  createdAt: Date;
}

export class ZapService {
  private static readonly ZAP_API_KEY = process.env.ZAP_API_KEY || 'AgentqSuperAI';
  private static readonly BACKEND_URL = process.env.AGENTQ_API_URL || process.env.BACKEND_URL || 'http://localhost:3010';

  // Track active ZAP containers by CLIENT_ID for complete isolation
  private static activeContainers: Map<string, ZapContainerInfo> = new Map();

  // Port range for dynamic ZAP container allocation
  private static readonly ZAP_PORT_RANGE_START = 8081;
  private static readonly ZAP_PORT_RANGE_END = 8200;
  private static usedPorts: Set<number> = new Set();

  // ZAP operation queue to prevent concurrent scans from interfering
  private static zapOperationQueue: Array<() => Promise<any>> = [];
  private static isZapBusy = false;

  /**
   * Queue ZAP operations to prevent concurrent scan interference
   * This ensures only one ZAP operation runs at a time
   */
  private static async queueZapOperation<T>(operation: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.zapOperationQueue.push(async () => {
        try {
          const result = await operation();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processZapQueue();
    });
  }

  /**
   * Process the ZAP operation queue
   */
  private static async processZapQueue(): Promise<void> {
    if (this.isZapBusy || this.zapOperationQueue.length === 0) {
      return;
    }

    this.isZapBusy = true;
    const operation = this.zapOperationQueue.shift();

    if (operation) {
      try {
        await operation();
      } catch (error) {
        console.error('ZAP operation failed:', error);
      }
    }

    this.isZapBusy = false;

    // Process next operation if any
    if (this.zapOperationQueue.length > 0) {
      setTimeout(() => this.processZapQueue(), 100); // Small delay between operations
    }
  }

  /**
   * Create dedicated ZAP container for CLIENT_ID complete isolation
   * This prevents reports from different users/companies from mixing
   */
  private static async createZapContainer(clientId: string): Promise<ZapContainerInfo | null> {
    try {
      console.log(`🐳 Creating dedicated ZAP container for CLIENT_ID: ${clientId}`);

      // Allocate dynamic port for this container
      const zapPort = await this.allocatePort();
      if (!zapPort) {
        console.error(`❌ No available ports for ZAP container (CLIENT_ID: ${clientId})`);
        return null;
      }

      const containerName = `zap-scan-${clientId}`;

      // Create ZAP container with dedicated port
      const { exec } = require('child_process');
      const dockerCommand = `docker run -d --name ${containerName} -p ${zapPort}:8080 -i zaproxy/zap-stable zap.sh -daemon -host 0.0.0.0 -port 8080 -config api.addrs.addr.name=.* -config api.addrs.addr.regex=true -config api.key=${this.ZAP_API_KEY}`;

      console.log(`🚀 Executing Docker command: ${dockerCommand}`);

      const containerId = await new Promise<string>((resolve, reject) => {
        exec(dockerCommand, (error: any, stdout: string, stderr: string) => {
          if (error) {
            console.error(`❌ Docker container creation failed:`, error);
            reject(error);
            return;
          }

          const containerId = stdout.trim();
          console.log(`✅ ZAP container created: ${containerId} (port: ${zapPort})`);
          resolve(containerId);
        });
      });

      const containerInfo: ZapContainerInfo = {
        containerId,
        containerName,
        port: zapPort,
        host: `http://localhost:${zapPort}`,
        clientId,
        createdAt: new Date()
      };

      // Wait for ZAP container to be ready
      await this.waitForZapReady(containerInfo);

      // Track active container
      this.activeContainers.set(clientId, containerInfo);
      console.log(`🔒 ZAP container ready for CLIENT_ID ${clientId}: ${containerInfo.host}`);

      return containerInfo;
    } catch (error) {
      console.error(`❌ Error creating ZAP container for CLIENT_ID ${clientId}:`, error);
      return null;
    }
  }

  /**
   * Allocate dynamic port for ZAP container
   */
  private static async allocatePort(): Promise<number | null> {
    for (let port = this.ZAP_PORT_RANGE_START; port <= this.ZAP_PORT_RANGE_END; port++) {
      if (!this.usedPorts.has(port)) {
        // Check if port is actually available
        const isAvailable = await this.isPortAvailable(port);
        if (isAvailable) {
          this.usedPorts.add(port);
          console.log(`📍 Allocated port ${port} for ZAP container`);
          return port;
        }
      }
    }
    console.error(`❌ No available ports in range ${this.ZAP_PORT_RANGE_START}-${this.ZAP_PORT_RANGE_END}`);
    return null;
  }

  /**
   * Check if port is available
   */
  private static async isPortAvailable(port: number): Promise<boolean> {
    return new Promise((resolve) => {
      const net = require('net');
      const server = net.createServer();

      server.listen(port, () => {
        server.once('close', () => {
          resolve(true);
        });
        server.close();
      });

      server.on('error', () => {
        resolve(false);
      });
    });
  }

  /**
   * Wait for ZAP container to be ready
   */
  private static async waitForZapReady(containerInfo: ZapContainerInfo): Promise<void> {
    const maxRetries = 30; // 30 seconds timeout
    const retryInterval = 1000; // 1 second

    console.log(`⏳ Waiting for ZAP container to be ready: ${containerInfo.host}`);

    for (let i = 0; i < maxRetries; i++) {
      try {
        const response = await axios.get(
          `${containerInfo.host}/JSON/core/view/version/?apikey=${this.ZAP_API_KEY}`,
          { timeout: 2000 }
        );

        if (response.status === 200) {
          console.log(`✅ ZAP container ready: ${containerInfo.host} (version: ${response.data.version})`);
          return;
        }
      } catch (error) {
        // ZAP not ready yet, continue waiting
      }

      await new Promise(resolve => setTimeout(resolve, retryInterval));
    }

    throw new Error(`❌ ZAP container failed to start within ${maxRetries} seconds: ${containerInfo.host}`);
  }

  /**
   * Release allocated port
   */
  private static releasePort(port: number): void {
    this.usedPorts.delete(port);
    console.log(`🔓 Released port ${port}`);
  }

  /**
   * Aggressively clear all ZAP data to prevent cross-contamination
   * @deprecated - Not needed with container isolation
   */
  private static async clearAllZapData(): Promise<void> {
    try {
      console.log('🧹 Clearing all ZAP data to prevent cross-contamination...');

      // Clear sites tree
      await axios.get(`${this.ZAP_HOST}/JSON/core/action/clearExcludedFromProxy/?apikey=${this.ZAP_API_KEY}`);

      // Clear all alerts
      await axios.get(`${this.ZAP_HOST}/JSON/core/action/deleteAllAlerts/?apikey=${this.ZAP_API_KEY}`);

      // Clear history
      await axios.get(`${this.ZAP_HOST}/JSON/core/action/clearHistory/?apikey=${this.ZAP_API_KEY}`);

      // Clear spider results
      await axios.get(`${this.ZAP_HOST}/JSON/spider/action/clearExcludedFromScan/?apikey=${this.ZAP_API_KEY}`);

      // Clear active scan results
      await axios.get(`${this.ZAP_HOST}/JSON/ascan/action/clearExcludedFromScan/?apikey=${this.ZAP_API_KEY}`);

      console.log('✅ All ZAP data cleared');
    } catch (error) {
      console.warn('⚠️ Some ZAP data clearing operations failed (continuing):', (error as Error).message);
    }
  }

  /**
   * Verify that ZAP has scan data available for report generation
   */
  private static async verifyZapHasData(sessionName: string, clientId: string): Promise<boolean> {
    try {
      // Check if ZAP has any sites/URLs from the scan
      const sitesUrl = sessionName !== 'default'
        ? `${this.ZAP_HOST}/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}&session=${sessionName}`
        : `${this.ZAP_HOST}/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}`;

      const sitesResponse = await axios.get(sitesUrl);
      const sites = sitesResponse.data?.sites || [];

      console.log(`🔍 ZAP scan data check for CLIENT_ID ${clientId}: ${sites.length} sites found`);

      if (sites.length > 0) {
        console.log(`✅ ZAP has scan data for CLIENT_ID ${clientId}:`, sites);
        return true;
      } else {
        console.warn(`⚠️ ZAP has no scan data for CLIENT_ID ${clientId}`);
        return false;
      }
    } catch (error) {
      console.error(`❌ Error checking ZAP scan data for CLIENT_ID ${clientId}:`, (error as Error).message);
      return false;
    }
  }

  /**
   * Verify that the ZAP session is properly isolated
   */
  private static async verifySessionIsolation(sessionName: string, clientId: string): Promise<void> {
    try {
      // Check that sites are empty for new session
      const sitesUrl = `${this.ZAP_HOST}/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}&session=${sessionName}`;
      const sitesResponse = await axios.get(sitesUrl);

      const sites = sitesResponse.data?.sites || [];
      if (sites.length > 0) {
        console.warn(`⚠️ ZAP session ${sessionName} for CLIENT_ID ${clientId} is not clean - found ${sites.length} existing sites:`, sites);
      } else {
        console.log(`✅ ZAP session ${sessionName} for CLIENT_ID ${clientId} is clean`);
      }
    } catch (error) {
      console.warn(`⚠️ Could not verify session isolation for CLIENT_ID ${clientId}:`, (error as Error).message);
    }
  }

  /**
   * Clear stored reports for a test case to prevent contaminated data
   */
  private static async clearStoredReports(testCaseId: string): Promise<void> {
    try {
      console.log(`🧹 Clearing stored reports for test case: ${testCaseId} to prevent contamination`);

      // Call backend API to clear stored security reports for this test case
      const response = await axios.delete(
        `${this.BACKEND_URL}/temp-test-results/security-reports/${testCaseId}`,
        { timeout: 5000 }
      );

      if (response.status === 200) {
        console.log(`✅ Stored reports cleared for test case: ${testCaseId}`);
      } else {
        console.warn(`⚠️ Failed to clear stored reports for test case: ${testCaseId}`);
      }
    } catch (error) {
      console.warn(`⚠️ Could not clear stored reports for test case ${testCaseId}:`, (error as Error).message);
      // Don't throw error - continue with fresh report generation
    }
  }

  /**
   * Clean up ZAP session for a client
   */
  private static async cleanupClientSession(clientId: string): Promise<void> {
    try {
      const sessionName = this.activeSessions.get(clientId);
      if (sessionName && sessionName !== 'default') {
        // Remove session from ZAP
        await axios.get(
          `${this.ZAP_HOST}/JSON/core/action/removeSession/?apikey=${this.ZAP_API_KEY}&session=${sessionName}`
        );

        console.log(`🧹 Cleaned up ZAP session for CLIENT_ID ${clientId}: ${sessionName}`);
      }

      // Always remove from active sessions map
      this.activeSessions.delete(clientId);

      // Clear all ZAP data after session cleanup to ensure no residual data
      await this.clearAllZapData();
      console.log(`🔒 ZAP data cleared after session cleanup for CLIENT_ID ${clientId}`);
    } catch (error) {
      console.error(`❌ Error cleaning up ZAP session for CLIENT_ID ${clientId}:`, error);
      // Still remove from active sessions even if cleanup failed
      this.activeSessions.delete(clientId);
    }
  }

  /**
   * Generate ZAP security report for a completed test
   * @param clientId - Unique client ID for isolation
   * @param testCaseId - Test case ID
   * @returns Promise<ZapReportData | null>
   */
  static async generateSecurityReport(clientId: string, testCaseId: string): Promise<ZapReportData | null> {
    // Queue this ZAP operation to prevent concurrent scan interference
    return this.queueZapOperation(async () => {
      try {
        console.log(`🔍 Generating ZAP security report for client: ${clientId}, test case: ${testCaseId} (queued)`);

        // CRITICAL: First, fetch ZAP data while it still exists from the test execution
        console.log(`📊 Fetching ZAP scan data for CLIENT_ID: ${clientId} before any cleanup`);

        // Get existing session name if available, otherwise use default
        const existingSessionName = this.activeSessions.get(clientId) || 'default';
        console.log(`🔍 Using existing ZAP session: ${existingSessionName} for CLIENT_ID: ${clientId}`);

        // Verify that ZAP has scan data before proceeding
        const zapHealthCheck = await this.verifyZapHasData(existingSessionName, clientId);
        if (!zapHealthCheck) {
          console.warn(`⚠️ No ZAP scan data found for CLIENT_ID: ${clientId}, session: ${existingSessionName}`);
          return null;
        }

      // Get HTML report from ZAP (session-isolated)
      const htmlReport = await this.fetchZapHtmlReport(existingSessionName);
      if (!htmlReport) {
        console.warn('⚠️ No ZAP HTML report available');
        return null;
      }

      // Get JSON report from ZAP for detailed analysis (session-isolated)
      const jsonReport = await this.fetchZapJsonReport(existingSessionName);

      // Get scan context information (session-isolated)
      console.log('🔍 Fetching ZAP scan context...');
      const scanContext = await this.fetchZapScanContext(existingSessionName);
      console.log(`✅ Scan context fetched: ${scanContext.sites.length} sites, ${scanContext.urls.length} URLs`);

      // Parse vulnerabilities from the reports
      const vulnerabilities = this.parseVulnerabilities(htmlReport, jsonReport);
      const summary = this.generateSummary(vulnerabilities, scanContext);

      const reportData: ZapReportData = {
        html: htmlReport,
        json: jsonReport,
        timestamp: new Date().toISOString(),
        testCaseId,
        clientId,
        vulnerabilities,
        summary,
        scanContext
      };

      // Save report to client-isolated directory
      const reportPath = await this.saveReportToFile(reportData);
      console.log(`📊 ZAP report saved to: ${reportPath}`);

      // CRITICAL: Clear any old stored reports BEFORE storing fresh report
      await this.clearStoredReports(testCaseId);

      // Store fresh report URL in backend database
      await this.storeReportInDatabase(testCaseId, reportPath, reportData);

      // Clean up ZAP session after report generation to prevent buildup
      await this.cleanupClientSession(clientId);

        return reportData;
      } catch (error) {
        console.error('❌ Error generating ZAP security report:', error);
        // Clean up session even on error
        await this.cleanupClientSession(clientId);
        return null;
      }
    });
  }

  /**
   * Fetch HTML report from ZAP (session-isolated)
   */
  private static async fetchZapHtmlReport(sessionName?: string): Promise<string | null> {
    try {
      let url = `${this.ZAP_HOST}/OTHER/core/other/htmlreport/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        url += `&session=${sessionName}`;
      }

      const response = await axios.get(url, { timeout: 10000 });
      return response.data;
    } catch (error) {
      console.error('Error fetching ZAP HTML report:', error);
      return null;
    }
  }

  /**
   * Fetch JSON report from ZAP for detailed analysis (session-isolated)
   */
  private static async fetchZapJsonReport(sessionName?: string): Promise<any | null> {
    try {
      let url = `${this.ZAP_HOST}/JSON/core/view/alerts/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        url += `&session=${sessionName}`;
      }

      const response = await axios.get(url, { timeout: 10000 });
      return response.data;
    } catch (error) {
      console.error('Error fetching ZAP JSON report:', error);
      return null;
    }
  }

  /**
   * Fetch scan context information from ZAP (session-isolated)
   */
  private static async fetchZapScanContext(sessionName?: string): Promise<ZapScanContext> {
    const scanContext: ZapScanContext = {
      sites: [],
      urls: [],
      hosts: [],
      scanStartTime: new Date().toISOString(),
      scanEndTime: new Date().toISOString()
    };

    try {
      // Fetch sites that were accessed (session-isolated)
      let sitesUrl = `${this.ZAP_HOST}/JSON/core/view/sites/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        sitesUrl += `&session=${sessionName}`;
      }
      const sitesResponse = await axios.get(sitesUrl, { timeout: 5000 });
      if (sitesResponse.data && sitesResponse.data.sites) {
        scanContext.sites = sitesResponse.data.sites;
      }
    } catch (error) {
      console.warn('Error fetching ZAP sites:', error);
    }

    try {
      // Fetch URLs that were accessed (session-isolated)
      let urlsUrl = `${this.ZAP_HOST}/JSON/core/view/urls/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        urlsUrl += `&session=${sessionName}`;
      }
      const urlsResponse = await axios.get(urlsUrl, { timeout: 5000 });
      if (urlsResponse.data && urlsResponse.data.urls) {
        scanContext.urls = urlsResponse.data.urls;
      }
    } catch (error) {
      console.warn('Error fetching ZAP URLs:', error);
    }

    try {
      // Fetch hosts that were accessed (session-isolated)
      let hostsUrl = `${this.ZAP_HOST}/JSON/core/view/hosts/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        hostsUrl += `&session=${sessionName}`;
      }
      const hostsResponse = await axios.get(hostsUrl, { timeout: 5000 });
      if (hostsResponse.data && hostsResponse.data.hosts) {
        scanContext.hosts = hostsResponse.data.hosts;
      }
    } catch (error) {
      console.warn('Error fetching ZAP hosts:', error);
    }

    try {
      // Fetch spider results if available (session-isolated)
      let spiderUrl = `${this.ZAP_HOST}/JSON/spider/view/results/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        spiderUrl += `&session=${sessionName}`;
      }
      const spiderResponse = await axios.get(spiderUrl, { timeout: 5000 });
      if (spiderResponse.data) {
        scanContext.spiderResults = spiderResponse.data;
      }
    } catch (error) {
      console.warn('Error fetching ZAP spider results:', error);
    }

    try {
      // Fetch active scan results if available (session-isolated)
      let ascanUrl = `${this.ZAP_HOST}/JSON/ascan/view/scans/?apikey=${this.ZAP_API_KEY}`;
      if (sessionName && sessionName !== 'default') {
        ascanUrl += `&session=${sessionName}`;
      }
      const ascanResponse = await axios.get(ascanUrl, { timeout: 5000 });
      if (ascanResponse.data) {
        scanContext.activeScanResults = ascanResponse.data;
      }
    } catch (error) {
      console.warn('Error fetching ZAP active scan results:', error);
    }

    // Set target URL from the first site if available
    if (scanContext.sites.length > 0) {
      scanContext.targetUrl = scanContext.sites[0];
    }

    console.log(`📊 ZAP scan context: ${scanContext.sites.length} sites, ${scanContext.urls.length} URLs, ${scanContext.hosts.length} hosts`);

    return scanContext;
  }

  /**
   * Parse vulnerabilities from ZAP reports
   */
  private static parseVulnerabilities(htmlReport: string, jsonReport?: any): ZapVulnerability[] {
    const vulnerabilities: ZapVulnerability[] = [];

    try {
      // Parse from JSON report if available (more structured)
      if (jsonReport && jsonReport.alerts) {
        jsonReport.alerts.forEach((alert: any) => {
          const vulnerability: ZapVulnerability = {
            name: alert.name || 'Unknown Vulnerability',
            risk: this.mapRiskLevel(alert.risk),
            confidence: alert.confidence || 'Unknown',
            description: alert.description || '',
            solution: alert.solution || '',
            reference: alert.reference || '',
            instances: alert.instances?.map((instance: any) => ({
              uri: instance.uri || '',
              method: instance.method || '',
              param: instance.param || '',
              evidence: instance.evidence || ''
            })) || []
          };
          vulnerabilities.push(vulnerability);
        });
      } else {
        // Fallback to HTML parsing if JSON not available
        const htmlVulns = this.parseHtmlReport(htmlReport);
        vulnerabilities.push(...htmlVulns);
      }
    } catch (error) {
      console.error('Error parsing vulnerabilities:', error);
    }

    return vulnerabilities;
  }

  /**
   * Parse vulnerabilities from HTML report (fallback method)
   */
  private static parseHtmlReport(htmlReport: string): ZapVulnerability[] {
    const vulnerabilities: ZapVulnerability[] = [];

    try {
      // Simple regex-based parsing for HTML report
      const vulnRegex = /<h3[^>]*>([^<]+)<\/h3>[\s\S]*?Risk:\s*([^<\n]+)/gi;
      let match;

      while ((match = vulnRegex.exec(htmlReport)) !== null) {
        const name = match[1].trim();
        const risk = this.mapRiskLevel(match[2].trim());

        vulnerabilities.push({
          name,
          risk,
          confidence: 'Unknown',
          description: `Security vulnerability: ${name}`,
          instances: []
        });
      }
    } catch (error) {
      console.error('Error parsing HTML report:', error);
    }

    return vulnerabilities;
  }

  /**
   * Map ZAP risk levels to standardized format
   */
  private static mapRiskLevel(risk: string): 'High' | 'Medium' | 'Low' | 'Informational' {
    const riskLower = risk.toLowerCase();
    if (riskLower.includes('high')) return 'High';
    if (riskLower.includes('medium')) return 'Medium';
    if (riskLower.includes('low')) return 'Low';
    return 'Informational';
  }

  /**
   * Generate summary statistics from vulnerabilities
   */
  private static generateSummary(vulnerabilities: ZapVulnerability[], scanContext?: ZapScanContext): ZapSummary {
    const summary: ZapSummary = {
      totalIssues: vulnerabilities.length,
      highRisk: vulnerabilities.filter(v => v.risk === 'High').length,
      mediumRisk: vulnerabilities.filter(v => v.risk === 'Medium').length,
      lowRisk: vulnerabilities.filter(v => v.risk === 'Low').length,
      informational: vulnerabilities.filter(v => v.risk === 'Informational').length,
      urlsScanned: scanContext?.urls.length || 0
    };

    return summary;
  }

  /**
   * Save ZAP report to client-isolated file system
   */
  private static async saveReportToFile(reportData: ZapReportData): Promise<string> {
    try {
      // CRITICAL: Use CLIENT_ID from reportData to ensure proper isolation
      // This prevents cross-contamination between different users/companies in SaaS
      const clientId = reportData.clientId || process.env.CLIENT_ID || 'default';
      const outputDir = `test-results/${clientId}`;

      const zapReportsDir = path.join(outputDir, 'zap-reports');

      // Ensure directory exists
      if (!fs.existsSync(zapReportsDir)) {
        fs.mkdirSync(zapReportsDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const reportFileName = `zap-report-${reportData.testCaseId}-${timestamp}.json`;
      const reportPath = path.join(zapReportsDir, reportFileName);

      // Save complete report data as JSON
      fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));

      // Also save HTML report separately for easy viewing
      const htmlFileName = `zap-report-${reportData.testCaseId}-${timestamp}.html`;
      const htmlPath = path.join(zapReportsDir, htmlFileName);
      fs.writeFileSync(htmlPath, reportData.html);

      console.log(`📁 ZAP report saved to CLIENT_ID isolated path: ${reportPath}`);
      console.log(`🔒 CLIENT_ID: ${clientId} - Report isolation ensured`);
      return reportPath;
    } catch (error) {
      console.error('Error saving ZAP report to file:', error);
      throw error;
    }
  }

  /**
   * Store ZAP report reference in backend database
   */
  private static async storeReportInDatabase(testCaseId: string, reportPath: string, reportData: ZapReportData): Promise<void> {
    try {
      console.log(`💾 Storing ZAP report reference in database for test case: ${testCaseId}`);
      console.log(`📊 Report data summary: ${reportData.vulnerabilities.length} vulnerabilities, ${(reportData.scanContext.urls || []).length} URLs scanned`);

      const requestPayload = {
        testCaseId: testCaseId,
        zapReport: {
          reportPath: reportPath,
          summary: reportData.summary,
          vulnerabilities: reportData.vulnerabilities.slice(0, 10), // Store first 10 for quick access
          timestamp: reportData.timestamp,
          clientId: reportData.clientId,
          scanContext: {
            sites: reportData.scanContext.sites || [],
            urls: (reportData.scanContext.urls || []).slice(0, 50), // Store first 50 URLs for quick access
            hosts: reportData.scanContext.hosts || [],
            targetUrl: reportData.scanContext.targetUrl || null,
            urlsScanned: (reportData.scanContext.urls || []).length
          }
        }
      };

      console.log(`📤 Sending request to: ${this.BACKEND_URL}/temp-test-results/security-logs`);

      const response = await axios.post(
        `${this.BACKEND_URL}/temp-test-results/security-logs`,
        requestPayload,
        {
          headers: {
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      if (response.data.success) {
        console.log(`✅ ZAP report reference stored in database: ${response.data.data.logsSecurityUrl}`);
      } else {
        console.warn('⚠️ Failed to store ZAP report reference in database:', response.data.message);
      }
    } catch (error: any) {
      console.error('❌ Error storing ZAP report in database:', error);

      if (error.response) {
        console.error(`📤 HTTP ${error.response.status}: ${error.response.statusText}`);
        console.error(`📄 Response data:`, error.response.data);
      } else if (error.request) {
        console.error('📡 No response received from backend:', error.request);
      } else {
        console.error('⚙️ Request setup error:', error.message);
      }

      // Don't throw error - report is still saved locally
    }
  }

  /**
   * Initialize dedicated ZAP container for CLIENT_ID before starting new test
   * This ensures complete isolation between different users/companies
   */
  static async initializeClientContainer(clientId: string): Promise<ZapContainerInfo | null> {
    // Queue this ZAP operation to prevent concurrent container creation
    return this.queueZapOperation(async () => {
      try {
        console.log(`🔧 Initializing ZAP container for CLIENT_ID: ${clientId}... (queued)`);

        // Create dedicated ZAP container for this client
        const containerInfo = await this.createZapContainer(clientId);

        if (containerInfo) {
          console.log(`✅ ZAP container initialized for CLIENT_ID: ${clientId} on port ${containerInfo.port}`);
          return containerInfo;
        } else {
          console.error(`❌ Failed to create ZAP container for CLIENT_ID: ${clientId}`);
          return null;
        }
      } catch (error) {
        console.error(`❌ Failed to initialize ZAP container for CLIENT_ID ${clientId}:`, error);
        return null;
      }
    });
  }

  /**
   * Get ZAP container info for CLIENT_ID
   */
  static getClientContainer(clientId: string): ZapContainerInfo | null {
    return this.activeContainers.get(clientId) || null;
  }

  /**
   * Remove ZAP container for CLIENT_ID
   */
  static async removeClientContainer(clientId: string): Promise<void> {
    const containerInfo = this.activeContainers.get(clientId);
    if (!containerInfo) {
      console.log(`📝 No ZAP container found for CLIENT_ID: ${clientId}`);
      return;
    }

    try {
      console.log(`🗑️ Removing ZAP container for CLIENT_ID: ${clientId}`);

      const { exec } = require('child_process');
      const removeCommand = `docker rm -f ${containerInfo.containerName}`;

      await new Promise<void>((resolve, reject) => {
        exec(removeCommand, (error: any, stdout: string, stderr: string) => {
          if (error) {
            console.warn(`⚠️ Error removing container ${containerInfo.containerName}:`, error.message);
            // Don't reject - container might already be removed
          } else {
            console.log(`✅ ZAP container removed: ${containerInfo.containerName}`);
          }
          resolve();
        });
      });

      // Release the port
      this.releasePort(containerInfo.port);

      // Remove from active containers
      this.activeContainers.delete(clientId);

      console.log(`🧹 ZAP container cleanup completed for CLIENT_ID: ${clientId}`);
    } catch (error) {
      console.error(`❌ Error during container cleanup for CLIENT_ID ${clientId}:`, error);
    }
  }

  /**
   * Clear ZAP session before starting new test (legacy method - deprecated)
   * @deprecated Use initializeClientSession instead for proper CLIENT_ID isolation
   */
  static async clearZapSession(): Promise<void> {
    try {
      console.log('🧹 Clearing ZAP session for new test (legacy method)...');

      // Clear ZAP session
      await axios.get(
        `${this.ZAP_HOST}/JSON/core/action/newSession/?apikey=${this.ZAP_API_KEY}`,
        { timeout: 5000 }
      );

      console.log('✅ ZAP session cleared');
    } catch (error) {
      console.warn('⚠️ Failed to clear ZAP session:', error);
      // Don't throw error - test can continue
    }
  }
}
